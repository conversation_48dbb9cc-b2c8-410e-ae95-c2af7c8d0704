{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"motion-extensions": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "stylePreprocessorOptions": {"includePaths": ["node_modules/hint.css/src", "node_modules/novo-elements"]}, "assets": ["src/assets", "src/favicon.ico", "src/static"], "styles": ["src/styles.scss"], "scripts": ["node_modules/post-robot/dist/post-robot.min.js"], "aot": false, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"prod": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "staging": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "motion-extensions:build"}, "configurations": {"prod": {"browserTarget": "motion-extensions:build:production"}, "staging": {"browserTarget": "motion-extensions:build:staging"}, "local": {"browserTarget": "motion-extensions:build:local"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "motion-extensions:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["node_modules/post-robot/dist/post-robot.min.js"], "styles": ["src/styles.scss"], "assets": ["src/assets", "src/favicon.ico", "src/static"]}}}}, "motion-extensions-e2e": {"root": "e2e", "sourceRoot": "e2e", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "motion-extensions:serve"}}}}}, "defaultProject": "motion-extensions", "schematics": {"@schematics/angular:component": {"style": "scss"}}}