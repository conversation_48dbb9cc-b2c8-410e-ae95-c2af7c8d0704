/* You can add global styles to this file, and also import other style files */

@import "hint";
@import "novo-elements";

body {
  background-color: #ffffff;
}

.dropdown-container.data-table-dropdown {
  min-width: 300px !important;
  max-width: 300px !important;

  .dropdown-list-filter {
    .filter-search {
      .bhi-search {
        left: 260px !important;
      }
    }
  }
}

.custom-card {
  height: 100%;
}

.bhi-x {
  // display: flex;
  // justify-content: center;
  // align-items: center;
  // font-size: inherit;
  // line-height: inherit;
  // width: 1em;
  // height: 1em;
  // color: inherit;
  // outline: none;

  &::before {
    // font-family: Bullhorn-Glyphicons !important;
    // font-style: normal;
    // font-weight: normal !important;
    // font-variant: normal;
    // text-transform: none;
    // -webkit-font-smoothing: antialiased;
    // -moz-osx-font-smoothing: grayscale;
    // vertical-align: middle;
    // content: "\f246";
    content: "\f1b1";
  }
}
