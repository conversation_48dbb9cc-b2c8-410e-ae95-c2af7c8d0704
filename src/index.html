<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Platform Extension Starter (Static)</title>
    <base href="/" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <link href="//fonts.googleapis.com/css?family=Montserrat:100,300,400,500,700,900" rel="stylesheet" type="text/css" />
    <link href="//cdn.rawgit.com/bullhorn/bullhorn-icons/1.15.0/fonts/Bullhorn-Glyphicons.css" rel="stylesheet" type="text/css" />
    <script src="//cdn.jsdelivr.net/npm/ckeditor-full@4.7.3/ckeditor.js"></script>
    <script>
      CKEDITOR_CONFIGS = {
        default: {
          versionCheck: false,
        },
      };
    </script>
  </head>

  <body>
    <platform-root>
      <novo-loading
        style="
          position: absolute;
          margin: auto;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          justify-content: center;
          align-items: center;
        "
      >
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </novo-loading>
    </platform-root>
  </body>
</html>
