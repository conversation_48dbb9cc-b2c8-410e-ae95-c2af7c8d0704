<div *ngIf="!processing" class="container">
    <novo-non-ideal-state *ngIf="!hasData && !isEdit" icon="commission" title="No commissions exists." description="There are currently no commissions set for this placement.">
        <novo-button theme="primary" (click)="createNewVersion()" icon="plus" [disabled]="!canAdd">Add Commissions</novo-button>
    </novo-non-ideal-state>

    <header *ngIf="!isEdit && hasData" class="header">
        <novo-row>            
            <novo-label>Effective On:</novo-label>
            <novo-dropdown>
                <novo-button class="borderless-button" theme="secondary" icon="collapse">{{ effectiveDate }}</novo-button>                
                <novo-optgroup>
                  <novo-option *ngFor="let opt of effectiveDates; index as i"
                               (click)="changeEffectiveDate(opt)">
                    {{ opt.label }}
                  </novo-option>
                </novo-optgroup>
            </novo-dropdown>                  
            <button theme="standard" (click)="edit()" [disabled]="!canEdit">Edit</button>
            <button theme="primary" (click)="createNewVersion()" [disabled]="!canAdd">New Version</button>
            <button theme="icon" icon="refresh" (click)="refresh()" ></button>
        </novo-row>
    </header>

    <novo-form *ngIf="isEdit || hasData" [form]="form" layout="vertical">
        <div class="novo-form-row">
            <novo-control [form]="form" [control]="versionIdControl"></novo-control>
        </div>
        <div class="novo-form-row">
            <novo-control [form]="form" [control]="effectiveDateControl"></novo-control>
        </div>
    </novo-form>

    <novo-form *ngIf="isEdit || hasData" [form]="lines">
        <novo-control-group [canRemove]="canRemoveFunction" [initialValue]="initValue" [add]="addCommissionLine" [remove]="isEdit" edit="false" label="Commission Lines" key="commissionLines" [form]="lines" [controls]="lineControls"></novo-control-group>
    </novo-form>

    <footer *ngIf="isEdit" class="form-footer">
        <button theme="standard" (click)="cancel()" [disabled]="saving">Cancel</button>
        <button theme="primary" (click)="save()" [disabled]="saving">Save</button>
    </footer>
    
</div>

<novo-loading *ngIf="processing"
    style="position: absolute; margin: auto; top: 0; left: 0; right: 0; bottom: 0;display: flex;justify-content: center;align-items: center;">
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
</novo-loading>