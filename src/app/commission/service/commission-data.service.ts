import { Injectable } from '@angular/core';
import { AppBridgeService } from '../../tools/service/app-bridge.service';
import { Location } from '@angular/common';
import { parseEntityId, parseEntityType } from '../../tools/util/util';
import * as moment from 'moment';
import * as momenttz from 'moment-timezone';
import { EffectiveVersion, CommissionCard, CommissionLine } from '../commision.types';
import { environment } from '../../../environments/environment';
@Injectable()
export class CommissionDataService {
    private _entityId: number;
    private _entityType: string;
    private _commissionObject: string;
    private _typeMetaData:any;

    constructor(private appBridgeService: AppBridgeService, private location: Location) {
        this._entityId = parseEntityId(this.location);
        this._entityType = parseEntityType(this.location);
        this._commissionObject = environment.commissionsObject;


    }

    async getUserInfo(id) {
        const url = `entity/CorporateUser/${id}?fields=id,name,email,firstName,lastName,primaryDepartment(id,name),departmentIdList,userType(id,name)`;
        const bridge = await this.appBridgeService.promise();
        return (await bridge.httpGET(url)).data.data;
    }
    
    public intToDate(dateInt: number): Date {
        if (!dateInt) {
           return null;
        }
 
        return new Date(momenttz(new Date(dateInt)).tz('America/New_York').format('YYYY-MM-DD 00:00:00'));
    }

    public intToDateString(dateInt: number): string {
       if (!dateInt) {
          return null;
       }

       return momenttz(new Date(dateInt)).tz('America/New_York').format('MM/DD/YYYY');
    }

    public dateStringtoInt(dateStr: string): number {
        if (!dateStr) {
            return null;
        }

        return momenttz.tz(dateStr ,'MM/DD/YYYY', 'America/New_York').valueOf();
    }

    public dateToInt(dt: Date): number {
        if (!dt) {
            return null;
        }

        return this.dateStringtoInt(moment(dt).format('MM/DD/YYYY'));
    }


    private async hasChanged(original:CommissionCard): Promise<boolean> { 
        const commission: CommissionCard = await this.getCommissionData();
        if (commission.versions.length !== original.versions.length) {
            return true;
        }

        for (let i =0; i < commission.versions.length; i++) {
            if (commission.versions[i].versionId !== original.versions[i].versionId
                || commission.versions[i].lines.length !== original.versions[i].lines.length
            ) {
               return true;
            }
            
            const oLine = commission.versions[i].lines;
            const nLine = original.versions[i].lines
            for (let j =0; j < nLine.length; j++) {
                if (nLine[j].id != oLine[j].id || nLine[j].lastModifiedDate != oLine[j].lastModifiedDate) {
                    return true;
                }
            }


        }
        

        return false;
    }

    public save(commission: CommissionCard, version: EffectiveVersion, effectiveDate: Date, lines: any): any {
        const linesToInsert = [];
        const linesToDelete = [];
        const linesToUpdate = [];
        const response = {isValid: true, error: ""};

        const effectiveDateInt = this.dateToInt(effectiveDate);                
        let versionId = (!version || version === undefined) ? 0 : null;
        
        const newCommission: CommissionCard = JSON.parse(JSON.stringify(commission));        

        if (versionId === 0) {            
            newCommission.versions.push({
                effectiveDate: effectiveDateInt,
                versionId: 0,
                effectiveEndDate: this.dateStringtoInt('12/31/2999'),
                versionStatus: "New",
                isDefaultVersion: false,
                lines: []
            })
        } else {
            newCommission.versions.forEach(e => {
               if (e.versionId === version.versionId) {
                   e.effectiveDate= effectiveDateInt;
               }                    
            });
        }

        const newVersions = newCommission.versions.sort((a, b) => {return (a.effectiveDate - b.effectiveDate);});        

        for (let i=0; i < newVersions.length; i++)  {
            if (i == newVersions.length -1) {
                newVersions[i].effectiveEndDate =  this.dateStringtoInt('12/31/2999');
            } else {
                const endDt = this.intToDate(newVersions[i+1].effectiveDate);
                endDt.setDate(endDt.getDate() - 1);
                newVersions[i].effectiveEndDate =  this.dateToInt(endDt);
            }

            if (versionId !== null && newVersions[i].versionId >= versionId) {
                versionId = newVersions[i].versionId + 1;
            }

            if (i > 0 && newVersions[i].effectiveDate == newVersions[i-1].effectiveDate ) {
                response.isValid = false;
                response.error += 'The effective date already exists.\n';
            }
        }                                
        
        const lineGroupByTypeResource = new Map();
        const limitsValidations = new Map();
        let requiredTypes = [];
        console.log('this._typeMetaData', this._typeMetaData);
        if (this._typeMetaData && this._typeMetaData.description && this._typeMetaData.description.length > 0) {
            const validations = JSON.parse(this._typeMetaData.description);
            if (validations.limits && validations.limits.length > 0) {
                validations.limits.forEach(l => {
                    const checkArr = limitsValidations.get(l.types);
                    if (!checkArr) {
                        limitsValidations.set(l.types, [{operator: l.operator, expectedValue: l.value, actualValue: 0}]);
                    } else {
                        checkArr.push({operator: l.operator, expectedValue: l.value, actualValue: 0});                        
                    }                    
                });
            }            
            
            if (validations.required && validations.required.length > 0) {
                requiredTypes = validations.required;                
            }
        }
                
        let hasNegValues = false;
        lines.commissionLines.forEach(l => {
            if (l.splitPercentage <= 0) {
                hasNegValues = true;
            }

            limitsValidations.forEach((v, k) => {
                if (k.includes(l.type) || k.length === 0) {
                    v.forEach(lv => {
                        lv.actualValue += l.splitPercentage;
                    });
                }
            });
            
            let  count = lineGroupByTypeResource.get(`${l.type}_${l.resource.id}`);
            if (!count || count === undefined) {
                count = 0;                
            } 
            lineGroupByTypeResource.set(`${l.type}_${l.resource.id}`, count + 1);
        });

        if (hasNegValues) {
            response.isValid = false;
            response.error += 'Split percentage cannot be negative.\n';
        }

        if ([...lineGroupByTypeResource.values()].find(i => {return i > 1;}) !== undefined) {
            response.isValid = false;
            response.error += 'More than one comission line defined for the same type and resource.\n';
        }

        console.log('limitsValidations', limitsValidations);

        limitsValidations.forEach((v, k) => {
            v.forEach(lv => {
                if (lv.operator === 'EQ' && (lv.actualValue*100) !== lv.expectedValue) {
                    response.isValid = false;
                    response.error += `Total split percentage for commission types${(k.length !== 0) ? ' (' + k.join(', ') + ')' : ''} must be equal to ${lv.expectedValue}%.\n`;
                } else if (lv.operator === 'GT' && (lv.actualValue*100) <= lv.expectedValue) {
                    response.isValid = false;   
                    response.error += `Total split percentage for commission types${(k.length !== 0) ? ' (' + k.join(', ') + ')' : ''} must be greater than  ${lv.expectedValue}%.\n`;
                } else if (lv.operator === 'LT' && (lv.actualValue*100) >= lv.expectedValue) {
                    response.isValid = false;   
                    response.error += `Total split percentage for commission types${(k.length !== 0) ? ' (' + k.join(', ') + ')' : ''} must be less than  ${lv.expectedValue}%.\n`;
                } else if (lv.operator === 'LE' && (lv.actualValue*100) > lv.expectedValue) {
                    response.isValid = false;   
                    response.error += `Total split percentage for commission types${(k.length !== 0) ? ' (' + k.join(', ') + ')' : ''} must not exceed ${lv.expectedValue}%.\n`;
                } else if (lv.operator === 'GE' && (lv.actualValue*100) < lv.expectedValue) {
                    response.isValid = false;   
                    response.error += `Total split percentage for commission types${(k.length !== 0) ? ' (' + k.join(', ') + ')' : ''} must be at least ${lv.expectedValue}%.\n`;
                }                
            });
        });        
        
        requiredTypes.forEach(rt => {
            const l = lines.commissionLines.filter((l) => {
                return l.type === rt;
            });

            if (l.length === 0) {
                response.isValid = false;
                response.error += `At least one ${rt} commission type line must exist.\n`;                    
            }            
        });        

        if (!response.isValid) {
            return Promise.resolve(response);
        }

        newVersions.forEach(e => {
            if (e.versionId === 0) {
                lines.commissionLines.forEach(l => {
                    linesToInsert.push(
                        {
                            int1: versionId,
                            float1: l.splitPercentage,
                            text1: `${l.resource.firstName} ${l.resource.lastName}`,
                            int2: l.resource.id,
                            text2: l.type,
                            date1: e.effectiveDate,
                            date2: e.effectiveEndDate
                        }
                    );
                });
                
            } else {            
                const origVersion = commission.versions.find(v => {return e.versionId === v.versionId});
                if (!version || version === undefined || origVersion.versionId !== version.versionId) {
                    if (origVersion.effectiveEndDate !== e.effectiveEndDate) {
                        e.lines.forEach(l => {
                            linesToUpdate.push(
                                {
                                    id: l.id,                            
                                    date2: e.effectiveEndDate
                                }
                            );
                        });
                    }                
                } else {
                    e.lines.forEach(l => {
                        const newLine = lines.commissionLines.find(cl => {
                          return l.id === cl.id 
                        });
          
                        if (!newLine || newLine === undefined) {
                          linesToDelete.push(l.id);                          
                        } else {
                            linesToUpdate.push(
                                {
                                    id: l.id,
                                    int1: e.versionId,
                                    float1: newLine.splitPercentage,
                                    text1: (newLine.resource.firstName) ? `${newLine.resource.firstName} ${newLine.resource.lastName}` : l.resourceName,
                                    int2: (newLine.resource.firstName) ? newLine.resource.id : l.resourceId,
                                    text2: newLine.type,
                                    date1: e.effectiveDate,
                                    date2: e.effectiveEndDate
                                }                          
                            );
                        }       
                    }); 

                    lines.commissionLines.forEach(l => {
                        if (!l.id || l.id === '') {
                            linesToInsert.push(
                                {
                                    int1: e.versionId,
                                    float1: l.splitPercentage,
                                    text1: `${l.resource.firstName} ${l.resource.lastName}`,
                                    int2: l.resource.id,
                                    text2: l.type,
                                    date1: e.effectiveDate,
                                    date2: e.effectiveEndDate
                                }
                            );
        
                        }
                    });
                }
            }
            
        });        

        return this.hasChanged(commission).then(changed => {
            if (changed) {
                response.isValid = false;
                response.error = 'Commissons have been updated by another user.\n';
                return Promise.resolve(response);
            }

            const promises = [];
            linesToInsert.forEach(i => {
                promises.push(this.insertEntity(this._commissionObject, i));            
            });

            linesToUpdate.forEach(i => {
                promises.push(this.updateEntity(this._commissionObject, i.id, i));
            });

            linesToDelete.forEach(i => {
                promises.push(this.deleteEntity(this._commissionObject, i));
            });

            return Promise.all(promises).then(responses => {
                return Promise.resolve(response);
            });
        });
    }

    public getEffectiveDateOptions(commission: CommissionCard): any {
        return commission.versions.map(e => {
            return {                
                label: `${this.intToDateString(e.effectiveDate)} (${e.versionStatus})`,
                value: e.effectiveDate,
            };
        });
    }

    public getEffectiveVersion(commission: CommissionCard, effectiveDate: number): EffectiveVersion {
        return commission.versions.find(e => {
            return e.effectiveDate === effectiveDate;
        });
    }
    
    public getDefaultEffectiveVersion(commission: CommissionCard): EffectiveVersion {        
        return commission.versions.find(e => {
            return e.isDefaultVersion;
        });
    }

    public async getPlacementData(): Promise<any> {
        const entity = 'Placement';        
        const fields = 'dateBegin';
        
        
        const data = await this.getEntity(entity, this._entityId, fields);        
                
        return data;
    }

    public async getCommissionData(): Promise<CommissionCard> {        
        const entity = this._commissionObject;
        const where = `placement.id = ${this._entityId}`;
        const fields = 'id,dateLastModified,int1,float1,text1,int2,text2,date1,date2';
        
        let data = null;
        let start = 0;
        const allData = [];
        while (!data || data.length === 500) {
            data = await this.queryEntity(entity, fields, where, 500, start);
            allData.push(...data);
            start += data.length;
        }
                
        return this.transformCommissionData(allData);
    }

    private transformCommissionData(data:any): CommissionCard {
        
        const currentDate = Date.now();

        const versionMap = new Map();
        data.forEach((i:any) => {
            const version : EffectiveVersion =  versionMap.get(i.int1);
            
            const commissionLines : CommissionLine[] = (version && version !== undefined) ? version.lines : [];
            const commissionLine : CommissionLine = {
                id: i.id,
                lastModifiedDate: i.dateLastModified, 
                resourceName: i.text1,
                resourceId: i.int2,
                commissionType: i.text2,
                splitPercent: i.float1 
            };

            commissionLines.push(commissionLine);            

            if (!version || version !== undefined) {                              
                versionMap.set(i.int1, {
                    isDefaultVersion: (i.date1 <= currentDate && i.date2 >= currentDate),
                    versionStatus: (i.date1 <= currentDate && i.date2 >= currentDate) ? "Current" : ((i.date2 < currentDate)? "Past" : "Future"),
                    effectiveDate: i.date1,
                    effectiveEndDate: i.date2,
                    versionId: i.int1,
                    lines: commissionLines
                });
            }
        });
        
        const versions = [...versionMap.values()].sort((a, b) => {return (a.effectiveDate - b.effectiveDate);});        

        let hasDefaultVersion = false;
        versions.forEach((e) => {
            e.lines.sort((a,b) => {return (a.commissionType < b.commissionType) ? -1 : ((a.commissionType === b.commissionType) ? a.id - b.id : 1);});
            hasDefaultVersion = (e.isDefaultVersion || hasDefaultVersion);
        });

        if (!hasDefaultVersion && versions.length > 0) {
            versions[versions.length-1].isDefaultVersion = true;
        }

        const commisionCard : CommissionCard = {versions: versions};

        return commisionCard;
    }

    public getEntity(entity: string, entityId: number, fields: string): any {
        const url = `entity/${entity}/${entityId}?fields=${fields}`;
        console.log('getEntity', url, fields);
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpGET(url)
                .then(result => {
                    return result.data.data;
                });
        });
    }

    public async getCommissionTypes(): Promise<any> {
        if (!this._typeMetaData) {
            this._typeMetaData = await this.getFieldMetaData(this._commissionObject, 'text2');            
        }

        return this._typeMetaData.options;        
    }

    private getFieldMetaData(entity: string, field: string): any {
        const url = `meta/${entity}?fields=${field}&meta=full`;
        
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpGET(url)
                .then(result => {                    
                    return result.data.fields[0];
                });
        });
    }

    public pickerLookup(entity: string, term: string, page: number, pageSize: number): any {
        const url = `lookup/expanded?entity=${entity}&filter=${term}&count=${pageSize}&start=${(page - 1) * pageSize}`;
        
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpGET(url).then(result => {                
                return result;
            });
        });
    }

    public insertEntity(entity: string, payload: any): any {
        const url = `entity/${entity}`;
        
        payload['placement'] = {id: this._entityId};
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpPUT(url, payload).then(response => {
                return response;
            });
        });
    }

    public updateEntity(entity: string, entityId: string, payload: any): any {
        const url = `entity/${entity}/${entityId}`;

        return this.appBridgeService.promise().then(appBridge => {            
            return appBridge.httpPOST(url, payload).then(response => {
                return response;
            });
        });
    }

    public deleteEntity(entity: string, entityId: number): any {
        const url = `entity/${entity}/${entityId}`;
        
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpDELETE(url).then(response => {                
                return response;
            });
        });
    }

    public queryEntity(entity: string, fields: string, where: string, count = 500, start = 0): any {
        const url = `query/${entity}`;
        console.log('queryEntity', url, fields, where, count);
        return this.appBridgeService.promise().then(appBridge => {
            return appBridge.httpPOST(url, {
               fields,
               where,
               count,
               start
            }).then(result => {
                console.log('query result', result);
                return result.data.data;
            });
        });
    }

    public get entityType(): string {
        return this._entityType;
    }
}