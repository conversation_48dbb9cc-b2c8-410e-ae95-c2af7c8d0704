export interface CommissionLine {
  id: number;
  lastModifiedDate: number;
  resourceName: string;
  resourceId: number;
  commissionType: string;
  splitPercent: number;  
}

export interface EffectiveVersion {  
  isDefaultVersion: boolean;
  versionStatus: string;
  effectiveDate: number;
  effectiveEndDate: number;
  versionId: number;
  lines: CommissionLine[];
}

export interface CommissionCard {
  versions: EffectiveVersion[];  
}

