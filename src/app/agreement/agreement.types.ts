/**
 * Represents an Agreement entity associated with a Client Corporation
 * This custom object stores agreement details including contract terms, dates, and financial information
 */
export interface ClientCorporationCustomObjectInstance1 {
  /** Unique identifier for the agreement */
  id: number | null;

  /** The client corporation this agreement is associated with */
  clientCorporation: ClientCorporation | null;

  /** Expiration date of the agreement (timestamp) */
  date1: number | null;

  /** Effective date when the agreement begins (timestamp) */
  date2: number | null;

  /** Date when this record was created (timestamp) */
  dateAdded: number | null;

  /** Date when this record was last modified (timestamp) */
  dateLastModified: number | null;

  /** Direct hire fee percentage */
  float1: number | null;

  /** Contract limit length in months */
  float2: number | null;

  /** VMS fee percentage */
  float3: number | null;

  /** Overtime bill multiplier */
  float4: number | null;

  /** Dispute terms in days (see DisputeTerms enum) */
  int1: number | null;

  /** Name of the agreement */
  text1: string | null;

  /** Type of agreement (CRO, Direct Hire, Staffing, Consulting) */
  text2: string[] | null;

  /** Whether client logo and name approval is granted (Yes/No) */
  text3: string | null;

  /** Person who owns/manages this agreement */
  text4: string | null;

  /** Flat fee amount if applicable */
  text5: string | null;

  /** Remote reimbursement terms */
  text6: string | null;

  /** Contract template type used */
  text7: string | null;

  /** Staffing guarantee terms (hours) */
  text8: string | null;

  /** Price increase terms */
  text9: string | null;

  /** Affordable Care Act coverage (Yes/No) */
  text10: string | null;

  /** Direct hire guarantee period in days */
  text11: string | null;

  /** Affiliate addendum coverage details */
  text12: string | null;

  /** Subcontracting permissions */
  text13: string[] | null;

  /** AG sender corporate user */
  text14: string | null;

  /** AG signer corporate user */
  text15: string | null;

  /** Implied acceptance status */
  text16: string | null;

  /** AC confidentiality terms */
  text17: string | null;

  /** Invoice frequency (Weekly, Bi-Weekly, Monthly) */
  text18: string | null;

  /** Payment terms (Net 30, etc.) */
  text19: string | null;

  /** Client agreement point of contact */
  text20: string | null;

  /** Conversion schedule details for contractors converting to employees */
  textBlock1: string | null;

  /** General comments about the agreement */
  textBlock2: string | null;

  /** Additional contractual terms not covered by standard fields */
  textBlock3: string | null;

  /** Information about rebates associated with this agreement */
  textBlock4: string | null;

  /** Information about discounts associated with this agreement */
  textBlock5: string | null;
}

export interface ClientCorporation {
  id: number;
  name?: string;
}

// Option interfaces for fields with predefined values
export enum AgreementType {
  CRO = 'CRO/FSP',
  DirectHire = 'Direct Hire',
  Staffing = 'Staffing',
  Consulting = 'Consulting',
  CDA = 'CDA',
}

export enum YesNo {
  Yes = 'Yes',
  No = 'No',
}

export enum DisputeTerms {
  Empty = 1,
  Term15 = 15,
  Term30 = 30,
  Term45 = 45,
  Term60 = 60,
  Term90 = 90,
  Term120 = 120,
}

export enum DirectHireGuarantee {
  Days30 = '30 days',
  Days45 = '45 days',
  Days60 = '60 days',
  Days90 = '90 days',
  Other = 'Other',
  NA = 'N/A',
}

export enum PaymentTerms {
  DueUponReceipt = 'Due Upon Receipt',
  Net10 = 'Net 10',
  Net15 = 'Net 15',
  Net30 = 'Net 30',
  Net45 = 'Net 45',
  Net60 = 'Net 60',
  Net90 = 'Net 90',
  // Additional payment terms omitted for brevity
}

export interface ClientCorporationCustomObjectInstance3 {
  /** Unique identifier for the agreement */
  id: number | null;

  /** The client corporation this agreement is associated with */
  clientCorporation: ClientCorporation | null;

  /** Agreement ID related to ClientCorporationCustomObjectInstance1 */
  int1: number | null;

  /** Rebate/Discount */
  text4: string | null;
  // rebateDiscount: boolean | null;

  /** Not to Exceed ($) */
  text7: string | null;
  // rebateDiscountLimit: string | null;

  /** Business Unit */
  text1: string[] | null;
  // businessUnit: string[] | null;

  /** Agreement Status */
  text2: string | null;
  // agreementStatus: string | null;

  /** Agreement Expires */
  text3: string | null;
  // agreementExpires: boolean | null;

  /** Billing Deadline */
  int2: number | null;
  // billingDeadline: number | null;

  /** Rebate Type */
  text5: string | null;
  // rebateType: string | null;

  /** Discount Type */
  text6: string | null;
  // discountType: string | null;

  /** Additional Contractual Terms */
  textBlock1: string | null;
  // additionalContractualTerms: string | null;
}

// export interface AgreementCustomFields {
//   rebateDiscount: string | null; // boolean
//   rebateDiscountLimit: string | null;
//   businessUnit: string[] | null;
//   agreementStatus: string | null;
//   agreementExpires: string | null; // boolean
//   billingDeadline: number | null;
//   rebateType: string | null;
//   discountType: string | null;
//   additionalContractualTerms: string | null;
// }

export interface AgreementWithExtraFields {
  agreement: ClientCorporationCustomObjectInstance1;
  extraFields?: ClientCorporationCustomObjectInstance3;
}

export type SelectAgreementFields = Omit<
  ClientCorporationCustomObjectInstance1,
  'id' | 'clientCorporation' | 'dateAdded' | 'dateLastModified'
>;

export type SelectExtraFields = Omit<ClientCorporationCustomObjectInstance3, 'id' | 'clientCorporation' | 'int1'>;

export type AgreementCustomFields = {
  [K in keyof SelectExtraFields as `extraField-${K}`]: ClientCorporationCustomObjectInstance3[K];
};

export type AgreementFormData = SelectAgreementFields & AgreementCustomFields;
