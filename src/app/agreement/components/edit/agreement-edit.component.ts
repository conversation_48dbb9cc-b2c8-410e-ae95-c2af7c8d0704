import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NovoFieldset, NovoFormGroup } from 'novo-elements';
import { Meta } from '../../../common/service/query-base/query-base.types';
import { AgreementFormData } from '../../agreement.types';
import { FormUtils2 } from '../../../common/replacements/form-utils';
import { FieldState, fieldStatesByAgreementType } from '../config';
import { Validators } from '@angular/forms';

export interface AgreementSaveEvent {
  agreement: AgreementFormData;
}

@Component({
  selector: 'agreement-edit',
  templateUrl: './agreement-edit.component.html',
  styleUrls: ['./agreement-edit.component.scss'],
})
export class AgreementEditComponent implements OnInit {
  @Output()
  public onSave: EventEmitter<AgreementSaveEvent> = new EventEmitter<AgreementSaveEvent>();

  @Output()
  public onCancel: EventEmitter<void> = new EventEmitter<void>();

  @Input() public agreement?: AgreementFormData;
  @Input() public meta?: Meta;

  public loading = false;
  public dynamicFieldsets?: NovoFieldset[];
  public dynamicForm?: NovoFormGroup;

  get canSave(): boolean {
    if (this.loading) {
      return false;
    }
    return this.dynamicForm?.valid ?? false;
  }

  constructor(private formUtils: FormUtils2) {}

  ngOnInit(): void {
    // Initialize component
    this.loading = true;
    this.configureForm()
      .then(() => {
        this.loading = false;
      })
      .catch((error) => {
        console.error('Error configuring form:', error);
        this.loading = false;
      });
  }

  private configureForm(): Promise<void> {
    try {
      this.dynamicFieldsets = this.formUtils.toFieldSets(this.meta, '$ USD', {}, undefined);
      if (this.agreement) {
        this.formUtils.setInitialValuesFieldsets(this.dynamicFieldsets, this.agreement);
      }
      console.log('AgreementEditComponent > dynamicFieldsets:', this.dynamicFieldsets);
      this.dynamicForm = this.formUtils.toFormGroupFromFieldset(this.dynamicFieldsets);
      const typeField = this.dynamicForm.get('text2');
      if (typeField) {
        typeField.valueChanges.subscribe(() => {
          this.applyFieldStatesByAgreementType();
        });
      }
      console.log('AgreementEditComponent > dynamicForm:', this.dynamicForm);
    } catch (error) {
      console.error('AgreementEditComponent > Error configuring form:', error);
    }

    return Promise.resolve();
  }

  public applyFieldStatesByAgreementType(): void {
    if (!this.agreement || !this.dynamicForm) {
      return;
    }

    const type = this.dynamicForm.get('text2')?.value;
    for (const [fieldName, fieldStates] of Object.entries(fieldStatesByAgreementType)) {
      const field = this.dynamicForm.get(fieldName);
      if (!field) {
        continue;
      }

      const state = fieldStates[type] ?? FieldState.Optional;
      field.setValidators(state === FieldState.Required ? Validators.required : null);

      const shouldHide = state === FieldState.Hidden;
      if (shouldHide !== field.disabled) {
        shouldHide ? field.disable() : field.enable();
      }
    }

    this.dynamicForm.updateValueAndValidity();
  }

  public save(): void {
    if (!this.dynamicForm) {
      return;
    }

    if (!this.dynamicForm.valid) {
      this.formUtils.forceValidation(this.dynamicForm);
      return;
    }

    const agreement = this.dynamicForm.value;
    this.onSave.emit({ agreement });
  }

  public cancel(): void {
    this.onCancel.emit();
  }
}
