<article class="agreement-edit">
  <div class="agreement-edit__container" *ngIf="!loading && dynamicForm">
    <div class="agreement-edit__form">
      <novo-dynamic-form
        [autoFocusFirstField]="true"
        class="dynamic"
        [fieldsets]="dynamicFieldsets"
        [(form)]="dynamicForm"
        #myform
      ></novo-dynamic-form>
    </div>
    <div class="agreement-edit__actions">
      <novo-button theme="primary" icon="check" [disabled]="canSave" (click)="save()">Save</novo-button>
      <novo-button theme="secondary" [disabled]="loading" (click)="cancel()">Cancel</novo-button>

      <!-- <novo-button  theme="primary" icon="check" color="positive" (click)="save()" [disabled]="!myform.isValid() || loading"> Save </novo-button>
      <novo-button  theme="secondary" icon="decline" color="negative" (click)="cancel()" [disabled]="loading"> Cancel </novo-button> -->
    </div>
  </div>
</article>
