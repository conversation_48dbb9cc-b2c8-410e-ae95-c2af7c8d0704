import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { Meta, MetaResponseFields } from '../../../common/service/query-base/query-base.types';
import { AgreementFormData } from '../../agreement.types';
import { overviewHiddenFields } from '../config';
import { IDataTableColumn, IDataTablePaginationOptions, NovoDataTable } from 'novo-elements';
import { getCustomFieldName } from '../../services/agreement-meta';

export interface OpenAgreementEvent {
  agreement: AgreementFormData;
  mode?: 'overview' | 'edit';
}

@Component({
  selector: 'agreement-list',
  templateUrl: './agreement-list.component.html',
  styleUrls: ['./agreement-list.component.scss'],
})
export class AgreementListComponent implements OnInit, OnChanges {
  @Output()
  public onOpenAgreement: EventEmitter<OpenAgreementEvent> = new EventEmitter<OpenAgreementEvent>();

  @Input() public agreements?: AgreementFormData[];
  @Input() public meta?: Meta;

  @ViewChild('agreementsListTable')
  table: NovoDataTable<AgreementFormData>;

  public tableColumns: IDataTableColumn<AgreementFormData>[];
  public displayedColumns: (keyof AgreementFormData)[];

  public paginationOptions: IDataTablePaginationOptions = {
    theme: 'standard',
    pageSize: 50,
    pageSizeOptions: [10, 50, 100, 250, 500],
  };

  public processing = false;
  private tableColumnsHydrated = false;

  public rows = [];

  private columnsOverrides = new Map<string, Partial<IDataTableColumn<AgreementFormData>>>([
    [
      'text1',
      {
        type: 'link',
        handlers: {
          click: this.openAgreement.bind(this),
        },
      },
    ],
    ['id', { width: 80 }],
  ]);

  ngOnChanges(changes: SimpleChanges): void {
    if ('agreements' in changes) {
      this.rows = this.agreements.map((agreement) => ({
        ...agreement,
        textBlock1: this.stripHtml(agreement.textBlock1),
        textBlock2: this.stripHtml(agreement.textBlock2),
      }));
    }
  }

  ngOnInit(): void {
    // Initialize component
    this.processing = true;
    this.hydrateTableColumns().then(() => {
      this.processing = false;
    });
  }

  private async hydrateTableColumns(): Promise<void> {
    // Use `Meta` to poulate tableColumns, be sure to include options for filtering and sorting
    if (this.tableColumnsHydrated || !this.meta) {
      return Promise.resolve();
    }
    const tableColumns: IDataTableColumn<AgreementFormData>[] = [];
    const displayedColumns: (keyof AgreementFormData)[] = [
      'id',
      'text1',
      getCustomFieldName('text1'),
      getCustomFieldName('text2'),
      'date2',
      'date1',
      'text19',
      'text18',
    ];

    for (const field of this.meta.fields) {
      if (overviewHiddenFields.includes(field.name)) {
        continue;
      }

      const name = field.name as keyof AgreementFormData;
      if (!displayedColumns.includes(name)) {
        displayedColumns.push(name);
      }
      tableColumns.push(getColumnFromMetaField(field, this.columnsOverrides.get(name)));
    }

    this.tableColumns = tableColumns;
    this.displayedColumns = displayedColumns;
    this.tableColumnsHydrated = true;

    return Promise.resolve();
  }

  openAgreement({ row }: { row: AgreementFormData }): void {
    // Navigate to agreement details page
    console.log('Navigate to details for:', row);
    this.onOpenAgreement.emit({
      agreement: row,
      mode: 'overview',
    });
  }

  /**
   * Strips HTML tags from a string and returns plain text
   * @param html The HTML string to strip tags from
   * @returns Plain text with HTML tags removed
   */
  private stripHtml(html: string | null): string | null {
    if (!html) {
      return html;
    }

    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Extract plain text content
    return tempDiv.textContent || tempDiv.innerText || '';
  }
}

function mapMetaTypeToColumnType(field: MetaResponseFields): IDataTableColumn<AgreementFormData>['type'] {
  const type = field.dataSpecialization ?? field.dataType ?? field.type;

  // Need to handle HTML
  switch (type.toLowerCase()) {
    // dataSpecialization
    case 'float':
      return 'bigdecimal';
    case 'date':
      return 'date';
    // dataType
    case 'integer':
      return 'text';
    case 'double':
    case 'number':
      return 'number';
    case 'timestamp':
      return 'datetime';
    default:
      return 'text';
  }
}

function getColumnFromMetaField(
  field: MetaResponseFields,
  overrides?: Partial<IDataTableColumn<AgreementFormData>>,
): IDataTableColumn<AgreementFormData> {
  const column = {
    id: field.name,
    label: field.label,
    type: mapMetaTypeToColumnType(field),
    sortable: true,
    filterable: false, // FIXME: if field has options, set to true
    ...(overrides ?? {}),
  };

  if (field.associatedEntity) {
    column.format = '$id | $name';
  }

  return column;
}
