<article class="agreement-overview">
  <ul class="agreement-overview__list" *ngIf="mappedAgreement">
    <li class="agreement-overview__item" *ngFor="let item of mappedAgreement">
      <novo-label class="agreement-overview__label">{{ item.label }}</novo-label>
      <novo-value class="agreement-overview__value" [data]="item.value" [meta]="item.meta"></novo-value>
    </li>
  </ul>

  <novo-loading
    *ngIf="processing"
    style="
      position: absolute;
      margin: auto;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    "
  >
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
  </novo-loading>

  <div class="agreement-overview__actions">
    <novo-button theme="primary" icon="edit" (click)="onEdit()">Edit</novo-button>
    <novo-button theme="secondary" (click)="onClose()">Close</novo-button>

    <!-- <novo-button  theme="primary" icon="check" color="positive" (click)="save()" [disabled]="!myform.isValid() || loading"> Save </novo-button>
      <novo-button  theme="secondary" icon="decline" color="negative" (click)="cancel()" [disabled]="loading"> Cancel </novo-button> -->
  </div>
</article>
