import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Meta, MetaResponseFields } from '../../../common/service/query-base/query-base.types';
import { AgreementFormData } from '../../agreement.types';
import { overviewHiddenFields } from '../config';
import { metaFieldSort } from '../../services/agreement-meta';
import { AgreementDataService } from '../../services/agreement-data.service';

export interface AgreementActionEvent {
  agreement: AgreementFormData;
  action: 'edit' | 'delete' | 'close';
}

@Component({
  selector: 'agreement-overview',
  templateUrl: './agreement-overview.component.html',
  styleUrls: ['./agreement-overview.component.scss'],
})
export class AgreementOverviewComponent implements OnInit, OnChanges {
  @Output()
  public onAgreementAction: EventEmitter<AgreementActionEvent> = new EventEmitter<AgreementActionEvent>();

  @Input() public agreement?: AgreementFormData;
  @Input() public meta?: Meta;

  public processing = false;

  public mappedAgreement: { label: string; meta: MetaResponseFields; value: unknown }[] = [];

  constructor(private agreementDataService: AgreementDataService) {}

  async ngOnChanges(changes: SimpleChanges): Promise<void> {
    if ('agreement' in changes || 'meta' in changes) {
      this.processing = true;
      await this.mapAgreement();
      this.processing = false;
    }
  }

  async ngOnInit(): Promise<void> {
    this.processing = true;
    await this.mapAgreement();
    this.processing = false;
  }

  private async mapAgreement(): Promise<void> {
    const mapped: AgreementOverviewComponent['mappedAgreement'] = [];
    if (this.agreement && this.meta) {
      for (const field of this.meta.fields) {
        if (overviewHiddenFields.includes(field.name)) {
          continue;
        }

        let value = this.agreement[field.name];
        // console.log('AgreementOverviewComponent', value, field);
        // if (!value) {
        //   continue;
        // }

        if (field.name === 'text4') {
          // Make a call to the options service to get the value for text4
          // This is a placeholder for the actual implementation
          const name = await this.getAgreementOwner(value);
          value = name ?? value; // Fallback to the original value if no options are found
        }

        mapped.push({
          label: field.label,
          meta: {
            ...field,
            label: undefined,
          },
          value: value,
        });
      }

      // use metaFieldSort, will need to map label back in with meta
      mapped.sort(metaFieldSort((data) => ({ ...data.meta, label: data.label })));
    }
    this.mappedAgreement = mapped;

    return Promise.resolve();
  }

  public onClose(): void {
    if (this.agreement) {
      this.onAgreementAction.emit({ agreement: this.agreement, action: 'close' });
    }
  }

  public onEdit(): void {
    if (this.agreement) {
      this.onAgreementAction.emit({ agreement: this.agreement, action: 'edit' });
    }
  }

  private async getAgreementOwner(value?: string): Promise<string | undefined> {
    if (!value) {
      return value;
    }
    const id = parseInt(value, 10);
    if (isNaN(id)) {
      return value;
    }
    const entity = await this.agreementDataService.getAgreementOwner(id);
    if (!entity) {
      return undefined;
    }
    return entity.name;
  }
}
