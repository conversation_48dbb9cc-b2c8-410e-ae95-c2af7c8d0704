import { MetaResponseFields } from '../../common/service/query-base/query-base.types';
import { AgreementFormData, AgreementType, SelectAgreementFields } from '../agreement.types';
import { agreementsExtraFieldsPrefix, getCustomFieldName } from '../services/agreement-meta';

export const overviewHiddenFields: readonly string[] = ['clientCorporation'];

export const metaOverrides: Record<string, Partial<MetaResponseFields>> = {
  textBlock2: {
    dataSpecialization: 'HTML-MINIMAL',
    inputType: undefined,
  },
  additionalContractualTerms: {
    dataSpecialization: 'HTML-MINIMAL',
    inputType: undefined,
  },
  id: {
    sortOrder: -1,
  },
  // TODO: date2 needs to be required
};

export enum FieldState {
  Required = 'required',
  Optional = 'optional',
  Hidden = 'hidden',
}

export const fieldStatesByAgreementType: Record<keyof AgreementFormData, Record<AgreementType, FieldState>> = {
  text1: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  [getCustomFieldName('text1')]: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  [getCustomFieldName('text2')]: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  text2: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  [getCustomFieldName('text3')]: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  date1: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Optional,
  },
  text19: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text18: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text4: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  float3: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  float1: {
    [AgreementType.CRO]: FieldState.Hidden,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text11: {
    [AgreementType.CRO]: FieldState.Hidden,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text8: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  textBlock1: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text10: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text6: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  date2: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  text7: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  text16: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text14: {
    [AgreementType.CRO]: FieldState.Hidden,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text15: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  text20: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Required,
  },
  float4: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text9: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text12: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text13: {
    [AgreementType.CRO]: FieldState.Required,
    [AgreementType.DirectHire]: FieldState.Required,
    [AgreementType.Staffing]: FieldState.Required,
    [AgreementType.Consulting]: FieldState.Required,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  int1: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  [getCustomFieldName('int2')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  [getCustomFieldName('text4')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  [getCustomFieldName('text5')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  textBlock4: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  [getCustomFieldName('text6')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  textBlock5: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  [getCustomFieldName('text7')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  text3: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  textBlock3: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Optional,
  },
  textBlock2: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Optional,
  },
  [getCustomFieldName('textBlock1')]: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Optional,
  },
  text17: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Required,
  },
  text5: {
    [AgreementType.CRO]: FieldState.Hidden,
    [AgreementType.DirectHire]: FieldState.Hidden,
    [AgreementType.Staffing]: FieldState.Hidden,
    [AgreementType.Consulting]: FieldState.Hidden,
    [AgreementType.CDA]: FieldState.Hidden,
  },
  float2: {
    [AgreementType.CRO]: FieldState.Optional,
    [AgreementType.DirectHire]: FieldState.Optional,
    [AgreementType.Staffing]: FieldState.Optional,
    [AgreementType.Consulting]: FieldState.Optional,
    [AgreementType.CDA]: FieldState.Hidden,
  },
} as Record<keyof AgreementFormData, Record<AgreementType, FieldState>>;
