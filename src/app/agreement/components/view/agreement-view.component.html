<section class="agreement-view">
  <header class="agreement-view__header">
    <novo-toolbar accent="light" gap="md">
      <novo-title>{{ viewTitle }}</novo-title>
      <div class="agreement-view__toolbar">
        <button [disabled]="isAddMode" theme="primary" size="small" icon="add-thin" (click)="newAgreement()">Add</button>
        <novo-nav>
          <novo-tab [active]="isListMode" (click)="setMode('list')">List</novo-tab>
          <novo-tab *ngIf="!isAddMode" [disabled]="!activeAgreement" [active]="isOverviewMode" (click)="setMode('overview')"
            >Overview</novo-tab
          >
          <novo-tab *ngIf="!isAddMode" [disabled]="!activeAgreement" [active]="isEditMode" (click)="setMode('edit')">Edit</novo-tab>
          <novo-tab *ngIf="isAddMode" [active]="isAddMode" (click)="setMode('add')">Add</novo-tab>
        </novo-nav>
      </div>
    </novo-toolbar>
  </header>
  <div class="agreement-view__content">
    <agreement-list
      *ngIf="agreementHistory?.length"
      [hidden]="!isListMode"
      [agreements]="agreementHistory"
      [meta]="meta"
      (onOpenAgreement)="openAgreement($event)"
    ></agreement-list>
    <agreement-overview
      *ngIf="isOverviewMode"
      [agreement]="activeAgreement"
      [meta]="meta"
      (onAgreementAction)="onOverviewAction($event)"
    ></agreement-overview>
    <agreement-edit
      *ngIf="isAddOrEditMode"
      [agreement]="activeAgreement"
      [meta]="meta"
      (onSave)="onFormSave($event)"
      (onCancel)="onFormCancel()"
    ></agreement-edit>

    <div class="agreement-view__none" *ngIf="noAgreementFound">
      <novo-non-ideal-state title="No Agreements" *ngIf="noAgreementFound">
        <novo-text lineLength="large" marginAfter>You do not have any Agreements on this record yet </novo-text>
        <button theme="primary" icon="refresh-o" (click)="reset()">Refresh</button>
      </novo-non-ideal-state>
    </div>

    <novo-loading *ngIf="isBusy" class="agreement-view__loader">
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
      <span class="dot"></span>
    </novo-loading>
  </div>
</section>
