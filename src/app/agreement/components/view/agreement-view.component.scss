.agreement-view {
  display: flex;
  flex-direction: column;
  height: 100svh;
  overflow: hidden;
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;

  &, & > * {
    box-sizing: border-box;
  }

  &__header {
    flex: none;

    novo-toolbar {
      justify-content: space-between;
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    container-type: size;
  }

  &__none {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  &__loader {
    display: flex;
    position: absolute;
    margin: auto;
    inset: 0;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  &__toolbar {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
}

.cke_editable.cke_display_version_check-absolute:before {
  display: none !important;
}
