import { Component, OnInit } from '@angular/core';
import { FormUtils } from 'novo-elements';
import { AgreementDataService } from '../../services/agreement-data.service';
import { AgreementFormData, AgreementWithExtraFields, ClientCorporationCustomObjectInstance1 } from '../../agreement.types';
import { Meta, MetaResponseFields } from '../../../common/service/query-base/query-base.types';
import { metaFieldSort, mixinCustomFieldData, mixinCustomMetaFields } from '../../services/agreement-meta';
import { OpenAgreementEvent } from '../list/agreement-list.component';
import { AgreementActionEvent } from '../overview/agreement-overview.component';
import { AgreementSaveEvent } from '../edit/agreement-edit.component';

@Component({
  selector: 'agreement-view',
  templateUrl: './agreement-view.component.html',
  styleUrls: ['./agreement-view.component.scss'],
})
export class AgreementViewComponent implements OnInit {
  public initializing = false;
  public isBusy = false;
  public mode: 'overview' | 'edit' | 'list' | 'add' = 'list';

  public activeAgreement?: AgreementFormData;
  public rawAgreementHistory?: AgreementWithExtraFields[];
  public agreementHistory?: AgreementFormData[];
  public rawMeta?: Meta;
  public meta?: Meta;

  // Mode getters
  public get isListMode(): boolean {
    return this.mode === 'list';
  }

  public get isAddMode(): boolean {
    return this.mode === 'add';
  }

  public get isEditMode(): boolean {
    if (this.activeAgreement === undefined) {
      return false;
    }
    return this.mode === 'edit';
  }

  public get isAddOrEditMode(): boolean {
    return this.isAddMode || this.isEditMode;
  }

  public get isOverviewMode(): boolean {
    if (this.activeAgreement === undefined) {
      return false;
    }
    return this.mode === 'overview';
  }

  // Meta getters
  public get viewTitle(): string {
    // const title = this.rawMeta?.label ?? 'Agreement';
    const title = 'Agreements';
    if (!this.isListMode) {
      return `${title} - ${this.activeAgreement?.id ?? 'New'}`;
    }

    return title;
  }

  public get noAgreementFound(): boolean {
    return this.isBusy === false && (this.rawAgreementHistory === undefined || this.rawAgreementHistory.length === 0);
  }

  constructor(private agreementDataService: AgreementDataService, private formUtils: FormUtils) {
    // Initialize component
  }

  ngOnInit(): void {
    console.log('agreement-view - ngOnInit');
    void this.reset();
  }

  private async hydrateData(): Promise<void> {
    console.log('agreement-view - hydrateData');
    this.rawMeta = await this.agreementDataService.getAgreementMeta();
    if (this.rawMeta) {
      const metaFields = mixinCustomMetaFields(this.rawMeta.fields ?? []);
      metaFields.sort(metaFieldSort((field) => field));
      const newMeta = {
        ...this.rawMeta,
        fields: metaFields,
      };
      this.meta = newMeta;
    }

    this.rawAgreementHistory = await this.agreementDataService.getAgreementsWithExtraFields();
    if (this.rawAgreementHistory && this.rawAgreementHistory.length) {
      // this.agreementHistory = [...this.rawAgreementHistory] as AgreementFormData[];
      this.agreementHistory = this.rawAgreementHistory.map((a) => {
        return mixinCustomFieldData(a);
      });
    }

    // if (this.agreementHistory?.length) {
    //   this.activeAgreement = this.agreementHistory[0];
    // }

    return Promise.resolve();
  }

  public async reset(): Promise<void> {
    this.initializing = true;
    this.isBusy = true;
    void this.hydrateData()
      .then(() => {
        console.log('Agreement data hydrated');
        this.initializing = false;
        this.isBusy = false;
      })
      .catch((error) => {
        // TODO: Handle error
        console.error('Error hydrating agreement data:', error);
        this.initializing = false;
        this.isBusy = false;
      });
  }

  public setMode(mode?: AgreementViewComponent['mode']): void {
    console.log('AgreementViewComponent.setMode', mode);
    if (this.mode === mode) {
      return;
    }
    this.mode = mode ?? 'overview';
  }

  public newAgreement(): void {
    console.log('AgreementViewComponent.newAgreement');
    this.activeAgreement = undefined;
    this.setMode('add');
  }

  public openAgreement(event: OpenAgreementEvent): void {
    console.log('AgreementViewComponent.openAgreement', event);
    this.activeAgreement = this.agreementHistory?.find((agreement) => agreement.id === event.agreement.id);
    if (event.mode && event.mode !== this.mode) {
      this.setMode(event.mode);
    }
  }

  public onOverviewAction(event: AgreementActionEvent): void {
    console.log('AgreementViewComponent.onOverviewAction', event);
    if (event.action === 'close') {
      this.setMode('list');
      this.activeAgreement = undefined;
    } else if (event.action === 'edit') {
      this.setMode('edit');
    }
  }

  public onFormSave(event: AgreementSaveEvent): void {
    console.log('AgreementViewComponent.onFormSave', event);
    // this.setMode('overview');
    this.setMode(this.mode === 'add' ? 'list' : 'overview');
  }

  public onFormCancel(): void {
    console.log('AgreementViewComponent.onFormCancel');
    this.setMode(this.mode === 'add' ? 'list' : 'overview');
  }
}
