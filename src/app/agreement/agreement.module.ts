import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NovoElementsModule } from 'novo-elements';
import { AgreementViewComponent } from './components/view/agreement-view.component';
import { AgreementDataService } from './services/agreement-data.service';
import { AgreementEditComponent } from './components/edit/agreement-edit.component';
import { AgreementOverviewComponent } from './components/overview/agreement-overview.component';
import { AgreementListComponent } from './components/list/agreement-list.component';
import { FormUtils2 } from '../common/replacements/form-utils';
import { OptionsService2 } from '../common/replacements/options.service';

const routes = [{ path: 'tab', component: AgreementViewComponent, pathMatch: 'full' }];

@NgModule({
  declarations: [AgreementViewComponent, AgreementEditComponent, AgreementOverviewComponent, AgreementListComponent],
  imports: [RouterModule.forChild(routes), CommonModule, NovoElementsModule],
  providers: [AgreementDataService, FormUtils2, OptionsService2],
})
export class AgreementModule {}
