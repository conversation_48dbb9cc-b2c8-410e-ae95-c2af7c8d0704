import { MetaResponseFields } from '../../common/service/query-base/query-base.types';
import {
  ClientCorporationCustomObjectInstance3,
  AgreementFormData,
  ClientCorporationCustomObjectInstance1,
  AgreementCustomFields,
  AgreementWithExtraFields,
  SelectExtraFields,
} from '../agreement.types';

export const agreementsExtraFieldsPrefix = 'extraField-' as const;

export const omittedExtraFields = ['id', 'clientCorporation', 'int1'] as const;

export function getCustomFieldName(name: keyof SelectExtraFields): keyof AgreementCustomFields {
  return `${agreementsExtraFieldsPrefix}${name}` as keyof AgreementCustomFields;
}

export function mixinCustomMetaFields(fields: MetaResponseFields[]): MetaResponseFields[] {
  const newFields: MetaResponseFields[] = [];
  for (const field of fields) {
    newFields.push({
      ...field,
      name: getCustomFieldName(field.name as keyof SelectExtraFields),
    });
  }
  return newFields;
}

export function mixinCustomFieldData({ agreement, extraFields }: AgreementWithExtraFields): AgreementFormData {
  const agreementCustomFields = extraFields ? toAgreementCustomFields(extraFields) : undefined;

  return {
    ...agreement,
    ...agreementCustomFields,
  };
}

export function toAgreementCustomFields(agreement: ClientCorporationCustomObjectInstance3): AgreementCustomFields {
  const extraFields: Partial<AgreementCustomFields> = {};

  for (const entry of Object.entries(agreement)) {
    const [key, value] = entry as [keyof SelectExtraFields, SelectExtraFields[keyof SelectExtraFields]];
    if (omittedExtraFields.includes(key as any)) {
      continue;
    }
    const customFieldName = getCustomFieldName(key);
    (extraFields as any)[customFieldName] = value;
  }

  return extraFields as AgreementCustomFields;
}

export function extractFieldData(formData: AgreementFormData): AgreementWithExtraFields {
  const agreement = {};
  let extraFields: Record<string, any> | undefined;

  for (const [key, value] of Object.entries(formData)) {
    if (key.startsWith(agreementsExtraFieldsPrefix)) {
      extraFields ??= {};
      extraFields[key.replace(agreementsExtraFieldsPrefix, '')] = value;
    } else {
      agreement[key] = value;
    }
  }

  return {
    agreement: agreement as ClientCorporationCustomObjectInstance1,
    extraFields: extraFields as ClientCorporationCustomObjectInstance3 | undefined,
  };
}

export function metaFieldSort<T extends unknown>(mapper: (data: T) => MetaResponseFields): (a: T, b: T) => number {
  return (a, b) => {
    const aMeta = mapper(a);
    const bMeta = mapper(b);
    const orderA = aMeta.sortOrder;
    const orderB = bMeta.sortOrder;

    // fields with sortOrder are sorted before fields without sortOrder
    // fields with the same sortOrder are sorted by label
    if (orderA && orderB) {
      return orderA - orderB;
    }
    if (orderA) {
      return -1;
    }
    if (orderB) {
      return 1;
    }
    return aMeta.label.localeCompare(bMeta.label);
  };
}
