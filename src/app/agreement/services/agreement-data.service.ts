import { Injectable } from '@angular/core';
import { Location } from '@angular/common';
import { AppBridgeService } from '../../tools/service/app-bridge.service';
import {
  EntityChangeResponse,
  EntityListResponse,
  EntityResponse,
  ErrorResponse,
  Meta,
  MetaResponse,
} from '../../common/service/query-base/query-base.types';
import { QueryBaseService } from '../../common/service/query-base/query-base.service';
import { parseEntityType, parseEntityId } from '../../tools/util/util';
import {
  AgreementWithExtraFields,
  ClientCorporationCustomObjectInstance1,
  ClientCorporationCustomObjectInstance3,
} from '../agreement.types';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AgreementDataService extends QueryBaseService {
  private _entityType: string;
  private _entityId: number;

  constructor(protected appBridgeService: AppBridgeService, location: Location) {
    super(appBridgeService);
    this._entityType = parseEntityType(location);
    this._entityId = parseEntityId(location);
    console.log('AgreementDataService', this._entityType, this._entityId);
  }

  get entityType(): string {
    return this._entityType;
  }

  get entityId(): number {
    return this._entityId;
  }

  public async getAgreementsWithExtraFields(): Promise<AgreementWithExtraFields[] | undefined> {
    const agreements = await this.getAgreements();
    if (!agreements || !agreements.length) {
      return undefined;
    }

    const extendedAgreements = await Promise.all(agreements.map((agreement) => this.getExtendedAgreement(agreement.id)));
    return agreements.map((agreement, index) => {
      return {
        agreement,
        extraFields: extendedAgreements[index],
      };
    });
  }

  public async getAgreements(): Promise<ClientCorporationCustomObjectInstance1[] | undefined> {
    const query = {
      where: `clientCorporation.id=${this._entityId}`,
      fields: '*',
    };
    const result = await this.query<EntityListResponse<ClientCorporationCustomObjectInstance1>>(environment.agreementsObject, query);

    if ((result as ErrorResponse).error) {
      return undefined;
    }

    return (result as EntityListResponse<ClientCorporationCustomObjectInstance1>).data.data;
  }

  public async getAgreementMeta(): Promise<Meta | undefined> {
    const result = await this.meta(environment.agreementsObject, '*', true);

    if ((result as ErrorResponse).error) {
      return undefined;
    }

    return (result as MetaResponse).data;
  }

  public async getAgreementOwner(id: number, fields = 'id,name'): Promise<{ id: number; name: string } | undefined> {
    const entity = await this.fetchEntity<{ id: number; name: string }>('Person', id, fields);

    if ((entity as ErrorResponse).error) {
      return undefined;
    }

    return (entity as EntityResponse<{ id: number; name: string }>).data.data;
  }

  public async getExtendedAgreement(id: number | string): Promise<ClientCorporationCustomObjectInstance3 | undefined> {
    const query = {
      where: `int1=${id}`,
      fields: '*',
    };
    const result = await this.query<EntityListResponse<ClientCorporationCustomObjectInstance3>>(
      environment.agreementsExtendedObject,
      query,
    );

    if ((result as ErrorResponse).error) {
      return undefined;
    }

    const data = (result as EntityListResponse<ClientCorporationCustomObjectInstance3>).data.data;
    if (!data.length) {
      return undefined;
    }

    return data[0];
  }

  public async getAgreementExtendedMeta(): Promise<Meta | undefined> {
    const result = await this.meta(environment.agreementsExtendedObject, '*', true);

    if ((result as ErrorResponse).error) {
      return undefined;
    }

    return (result as MetaResponse).data;
  }

  public async saveAgreementWithExtraFields(agreementWithExtraFields: AgreementWithExtraFields): Promise<{
    agreement: EntityChangeResponse | ErrorResponse;
    extraFields: EntityChangeResponse | ErrorResponse | undefined;
  }> {
    const { agreement, extraFields } = agreementWithExtraFields;
    const clientCorporation = { id: this._entityId };

    let agreementResponse: EntityChangeResponse | ErrorResponse;
    if (agreement.id) {
      agreementResponse = await this.update(environment.agreementsObject, agreement.id, agreement);
    } else {
      agreementResponse = await this.create(environment.agreementsObject, { ...agreement, clientCorporation });
    }

    if ((agreementResponse as ErrorResponse).error) {
      return {
        agreement: agreementResponse,
        extraFields: undefined,
      };
    }

    const agreementId = (agreementResponse as EntityChangeResponse).data.changedEntityId;

    let extraFieldsResponse: EntityChangeResponse | ErrorResponse;
    if (extraFields !== undefined) {
      if (extraFields.id) {
        extraFieldsResponse = await this.update(environment.agreementsExtendedObject, extraFields.id, extraFields);
      } else {
        extraFieldsResponse = await this.create(environment.agreementsExtendedObject, {
          ...extraFields,
          clientCorporation,
          int1: agreementId,
        });
      }
    }

    return {
      agreement: agreementResponse,
      extraFields: extraFieldsResponse,
    };
  }
}
