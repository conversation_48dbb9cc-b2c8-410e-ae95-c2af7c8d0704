import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import {SafePipe} from './safe/safe.pipe';
import { GroupByPipe } from './group-by/group-by.pipe';
import { KeysPipe } from './keys/keys.pipe';
import { OrderByPipe } from './order-by/order-by.pipe';

@NgModule({
  imports: [
    CommonModule
  ],
  declarations: [
    SafePipe,
    GroupByPipe,
    KeysPipe,
    OrderByPipe
  ],
  exports: [
    SafePipe,
    GroupByPipe,
    KeysPipe
  ]
})
export class PipesModule { }
