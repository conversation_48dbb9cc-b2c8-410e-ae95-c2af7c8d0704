import { Pipe, PipeTransform } from '@angular/core';
import {<PERSON><PERSON>anitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl} from '@angular/platform-browser';

@Pipe({
  name: 'safe'
})
export class SafePipe implements PipeTransform {

  constructor(protected sanitizer: DomSanitizer) {}

  public transform(value: any, type: string): SafeHtml | SafeStyle | SafeScript | SafeUrl | SafeResourceUrl {
    switch (type) {
      case 'html': return this.sanitizer.bypassSecurityTrustHtml(value.trim());
      case 'style': return this.sanitizer.bypassSecurityTrustStyle(value.trim());
      case 'script': return this.sanitizer.bypassSecurityTrustScript(value.trim());
      case 'url': return this.sanitizer.bypassSecurityTrustUrl(value.trim());
      case 'resourceUrl': return this.sanitizer.bypassSecurityTrustResourceUrl(value.trim());

      default: throw new Error(`Invalid safe type specified: ${type}`);
    }
  }

}
