import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'orderBy'
})
export class OrderByPipe implements PipeTransform {

  transform(array: any, field: string): any[] {
    if (!Array.isArray(array)) {
      return;
    }

    array.sort((first: any, second: any) => {
      if (first[field] < second[field]) {
        return -1;
      } else if (first[field] > second[field]) {
        return 1;
      } else {
        return 0;
      }
    });

    return array;
  }

}
