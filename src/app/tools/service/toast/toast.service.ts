import {Injectable, ViewContainerRef} from '@angular/core';
import {NovoToastService} from 'novo-elements';


export class ToastService {

  constructor(private toastService: NovoToastService, private viewRef: ViewContainerRef) {}

  public success(message: string, title: string = 'Success', icon: string = 'check'): void {
    this.toastService.parentViewContainer = this.viewRef;
    this.toastService.alert({
      title: title,
      message: message,
      icon: icon,
      theme: 'success',
      position: 'growlTopRight'
    });
  }

  public danger(message: string, title: string = 'Error', icon: string = 'caution'): void {
    this.toastService.parentViewContainer = this.viewRef;
    this.toastService.alert({
      title: title,
      message: message,
      icon: icon,
      theme: 'danger',
      position: 'growlTopRight'
    });
  }

}
