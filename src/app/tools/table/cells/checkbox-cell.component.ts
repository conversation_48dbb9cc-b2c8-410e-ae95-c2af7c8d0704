import {Component} from '@angular/core';

import {BaseRenderer} from 'novo-elements';

@Component({
  selector: 'checkbox-cell',
  template: `
    <novo-checkbox [(ngModel)]="data.sanctioned" [label]="''" (ngModelChange)="click()"></novo-checkbox>
  `
})
export class CheckboxCellComponent extends BaseRenderer {

  constructor() {
    super();
  }

  click(): void {
      this.meta.updateSanction(this.data);
  }

}
