import { Component } from '@angular/core';

import { BaseRenderer } from 'novo-elements';
import { getNestedElement } from '../table.utils';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'link-cell',
  template: `
    <a (click)="onClick()">{{ getData() }}</a>
  `
})
export class LinkCellComponent extends BaseRenderer {

  constructor() {
    super();
  }

  onClick(): void {
    if (this.meta.onClick) {
      this.meta.onClick(this.data);
    }
  }

  public getData() {
    const result = getNestedElement(this.data, this.meta.name);

    return result;
  }

}
