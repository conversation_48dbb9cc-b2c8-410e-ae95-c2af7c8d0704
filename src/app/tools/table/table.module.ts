import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';

import {NovoElementProviders, NovoElementsModule} from 'novo-elements';

import {PreviewCellComponent} from './cells/preview-cell.component';
import {DeleteCellComponent} from './cells/delete-cell.component';
import {EditCellComponent} from './cells/edit-cell.component';
import {IconCellComponent} from './cells/icon-cell.component';
import {CheckboxCellComponent} from './cells/checkbox-cell.component';
import {FormsModule} from '@angular/forms';
import { LinkCellComponent } from './cells/link-cell.component';

@NgModule({
    imports: [
        CommonModule,
        NovoElementsModule,
        NovoElementProviders.forChild(),
        FormsModule
    ],
    declarations: [
        PreviewCellComponent,
        DeleteCellComponent,
        EditCellComponent,
        IconCellComponent,
        Link<PERSON>ellComponent,
        CheckboxCellComponent
    ],
    exports: [
        PreviewCellComponent,
        DeleteCellComponent,
        Edit<PERSON>ellComponent,
        IconCellComponent,
        LinkCellComponent,
        CheckboxCellComponent
    ]
})
export class TableModule { }
