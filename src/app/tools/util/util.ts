import { Location } from '@angular/common';

const ENTITY_ID = /EntityID=(.*?)(&|$)/i;
const USER_ID = /UserID=(.*?)(&|$)/i;
const ENTITY_TYPE = /EntityType=(.*?)(&|$)/i;

export function parseEntityId(location: Location): number {
  return parseNumber(location, ENTITY_ID);
}

export function parseUserId(location: Location): number {
  return parseNumber(location, USER_ID);
}

export function parseEntityType(location: Location): string {
  return parse(location, ENTITY_TYPE);
}

export function parse(location: Location, regex: RegExp): string {
  const regexResult = location.path().match(regex);

  if (regexResult && regexResult.length > 0) {
    return decodeURIComponent(regexResult[1]);
  }

  console.error(`Error parsing '${regex}' from URL: '${location.path()}'`);

  return '';
}

export function parseNumber(location: Location, regex: RegExp): number {
  const value = parse(location, regex);

  const numberValue: number = parseInt(value, 10);

  if (isNaN(numberValue)) {
    console.error(`Error parsing ${regex} to a number from URL: '${location.path()}'`);

    return 0;
  }

  return numberValue;
}

export function parseCommaSeparatedStrings(value: any): Array<string> {
  if (value) {
    if (Array.isArray(value)) {
      return value.map(item => {
        return item.toString();
      });
    } else if (typeof value === 'string') {
      return value.split(',');
    } else if (typeof value === 'number') {
      return [value.toString()];
    }
  }

  return [];
}
