import {Component} from '@angular/core';

import {NovoModalParams, NovoModalRef} from 'novo-elements';

import {StandardModalParams} from '../modal.types';

@Component({
  selector: 'error-modal',
  templateUrl: './error-modal.component.html',
  styleUrls: ['./error-modal.component.scss']
})
export class ErrorModalComponent {

  public modalParams: StandardModalParams;

  constructor(private modalRef: NovoModalRef, modalParams: NovoModalParams) {
    this.modalParams = StandardModalParams.fromNovo(modalParams);
  }

  close() {
    if (this.modalParams.onClose !== undefined) {
      this.modalParams.onClose(false);
    }

    this.modalRef.close(false);
  }
}
