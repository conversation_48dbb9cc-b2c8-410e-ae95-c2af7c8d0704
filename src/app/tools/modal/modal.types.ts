import {NovoModalParams} from 'novo-elements';
import {ModalParams} from 'novo-elements';

export class StandardModalParams implements CustomModalParams {

  public message: string;
  public header: string;
  public isConfirm = false;
  static fromNovo(params: NovoModalParams): StandardModalParams {
    return new StandardModalParams(
      params['message'],
      params['header'],
      params['isConfirm'],
      params['onClose']
    );
  }

  public onClose: (result: boolean) => void = () => {
  }

  constructor(message: string, header: string, isConfirm: boolean = false, onClose: (result: boolean) => void = () => {
  }) {
    this.message = message;
    this.header = header;
    this.isConfirm = isConfirm;
    this.onClose = onClose;
  }

}

export interface CustomModalParams extends ModalParams {

  message: string;
  header: string;
  isConfirm: boolean;
  onClose: (result: boolean) => void;

}
