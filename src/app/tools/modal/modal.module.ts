import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NovoElementProviders, NovoElementsModule } from 'novo-elements';

import { StandardModalComponent } from './standard/standard-modal.component';
import { ErrorModalComponent } from './error/error-modal.component';
import { ConfigureColumnsModalComponent } from './configure/configure-modal.component';

@NgModule({
  imports: [CommonModule, NovoElementsModule, NovoElementProviders.forChild()],
  declarations: [StandardModalComponent, ErrorModalComponent, ConfigureColumnsModalComponent],
  exports: [StandardModalComponent, ErrorModalComponent],
})
export class ModalModule {}
