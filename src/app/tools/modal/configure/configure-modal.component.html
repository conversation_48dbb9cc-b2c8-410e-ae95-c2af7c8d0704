<novo-modal>
  <header title="Configure Columns"
          theme="contact">
    <utils>
      <novo-action icon="times"
                  (click)="close()"></novo-action>
    </utils>
  </header>
  <section>
    <novo-list direction="vertical">
      <novo-list-item *ngFor="let column of columns">
        <novo-item-header>
              <novo-item-title>{{ column.id }}</novo-item-title>
              <novo-item-header-end>
                <novo-checkbox [(ngModel)]="column.enabled"></novo-checkbox>
              </novo-item-header-end>
          </novo-item-header>
      </novo-list-item>
    </novo-list>
  </section>
  <button theme="standard"
          (click)="close()">Cancel</button>
  <button theme="primary"
          color="success"
          icon="check"
          (click)="save()">Save</button>
</novo-modal>
