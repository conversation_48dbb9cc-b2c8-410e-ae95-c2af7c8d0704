import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { NovoElementProviders, NovoElementsModule } from 'novo-elements';
import { AppComponent } from './app.component';
import { AppBridgeService } from './tools/service/app-bridge.service';
import { SettingsService } from './tools/service/settings/settings.service';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule } from '@angular/forms';

const routes: Routes = [
  { path: 'agreement', loadChildren: () => import('./agreement/agreement.module').then((m) => m.AgreementModule) },
  { path: 'commission', loadChildren: () => import('./commission/commission.module').then((m) => m.CommissionModule) },
  { path: '', redirectTo: '', pathMatch: 'full' },
];

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    FormsModule,
    BrowserAnimationsModule,
    RouterModule.forRoot(routes, { useHash: true, relativeLinkResolution: 'legacy' }),
    HttpClientModule,
    NovoElementsModule,
    NovoElementProviders.forRoot(),
  ],
  providers: [AppBridgeService, SettingsService],
  bootstrap: [AppComponent],
})
export class AppModule {}
