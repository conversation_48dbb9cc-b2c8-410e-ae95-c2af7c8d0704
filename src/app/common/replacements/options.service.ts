// NG2
import { Injectable } from '@angular/core';
import { MetaResponseFields } from '../service/query-base/query-base.types';
import { AppBridgeService } from '../../tools/service/app-bridge.service';
// App

@Injectable()
export class OptionsService2 {
  constructor(private appBridgeService: AppBridgeService) {}

  getOptionsConfig(field: MetaResponseFields): any {
    return {
      field: 'value',
      format: '$label',
      options: (query) => {
        return new Promise((resolve, reject) => {
          if (query && query.length) {
            const exp = new RegExp('^(?:[a-z]+:)?//', 'i');
            let endpoint;
            console.log('OptionsService2 > getOptionsConfig > field:', field);
            if (exp.test(field.optionsUrl)) {
              const url = new URL(field.optionsUrl);
              url.searchParams.set('filter', query || '');
              endpoint = url.toString();
            } else {
              // Construct relative url (host will not be used but is required for construction)
              const url = new URL(`http://placeholder.com/${field.optionsUrl}`);
              url.searchParams.set('filter', query || '');
              endpoint = `${url.pathname}${url.search}`;
            }

            // we only need the URL from the `options/...` and on
            if (!endpoint.startsWith('options/')) {
              endpoint = endpoint.substring(endpoint.indexOf('options/'));
            }

            this.appBridgeService.promise().then((appBridge) => {
              return appBridge.httpGET(endpoint).then((response) => {
                resolve(response.data.data);
              }, reject);
            });
          } else {
            resolve([]);
          }
        });
      },
    };
  }
}
