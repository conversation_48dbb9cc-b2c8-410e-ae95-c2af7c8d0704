// common API request and response types

export interface QueryFields {
  where: string;
  fields: string;
  start?: number;
  count?: number;
  showTotalMatched?: boolean;
}

export interface QueryTotal {
  where: string;
  totalOnly: true;
}

export type QueryData = QueryFields | QueryTotal;

export interface Meta {
  entityMetaUrl?: string;
  entity?: string;
  label?: string;
  fields?: MetaResponseFields[];
  dateLastModified: string;
  staticTemplateName?: string;
  tabName?: string;
}

export interface MetaResponseFields {
  name: string;
  type: string;
  dataType?: string;
  dataSpecialization?: string;
  maxLength?: number;
  confidential?: boolean;
  optional?: boolean;
  label: string;
  required?: boolean;
  readOnly?: boolean;
  multiValue?: boolean;
  inputType?: string;
  options?: MetaOption[];
  optionsType?: string;
  optionsUrl?: string;
  hideFromSearch?: boolean;
  sortOrder?: number;
  hint?: string;
  description?: string;
  systemRequired?: boolean;
  shouldAddCustomEntityLabel?: boolean;
  associatedEntity?: Meta;
}

export interface MetaOption {
  value: unknown;
  label: string;
}

export interface MetaRequestFields {
  fields: string;
}

export interface TotalResponse {
  data: {
    total: number;
  };
}

export interface MetaResponse {
  data: Meta;
}

export interface EntityResponse<T> {
  data: {
    data: T;
  };
}

export interface EntityListResponse<T> {
  data: {
    start: number;
    count: number;
    data: T[];
  };
}

export interface EntityChangeResponse {
  data: {
    changedEntityId: number;
    changeType: 'INSERT' | 'UPDATE';
  };
}

export interface ErrorResponse {
  error: {
    errorMessage: string;
    errorMessageKey: string;
    errorCode: number;
  };
}
