import { AppBridgeService } from '../../../tools/service/app-bridge.service';
import {
  EntityChangeResponse,
  EntityListResponse,
  EntityResponse,
  ErrorResponse,
  MetaResponse,
  QueryData,
  QueryFields,
  TotalResponse,
} from './query-base.types';

export class QueryBaseService {
  constructor(protected appBridgeService: AppBridgeService) {}

  public queryFromTotal<T>(
    entity: string,
    query: QueryFields,
    options?: { pageSize?: number; maxRecords?: number },
  ): Promise<EntityListResponse<T> | ErrorResponse> {
    const queryData = {
      start: 0,
      count: options?.pageSize ?? 500,
      ...query,
    };

    return this.query<TotalResponse>(entity, {
      where: query.where,
      totalOnly: true,
    }).then((response) => {
      if ((response as ErrorResponse).error) {
        return response as ErrorResponse;
      }

      const total = (response as TotalResponse).data.total;
      if (total === 0) {
        return {
          data: {
            start: queryData.start,
            count: 0,
            data: [],
          },
        };
      }

      const max = options?.maxRecords ? Math.min(options.maxRecords, total) : total;
      const promises: Promise<ErrorResponse | EntityListResponse<T>>[] = [];
      for (let i = queryData.start; i < max; i += queryData.count) {
        const queryForPage = {
          ...queryData,
          start: i,
        };
        console.log('queryForPage', queryForPage);
        promises.push(this.query<EntityListResponse<T>>(entity, queryForPage));
      }

      return Promise.all(promises).then((responses) => {
        const data: T[] = responses.reduce((acc, rsp) => {
          if ((rsp as ErrorResponse).error) {
            console.warn(rsp);
            return acc;
          }
          const repsonseData = (rsp as EntityListResponse<T>).data?.data;
          if (repsonseData) {
            acc.push(...repsonseData);
          }
          return acc;
        }, []);

        return {
          data: {
            start: queryData.start,
            count: data.length,
            data: data,
          },
        };
      });
    });
  }

  public query<T>(entity: string, query: QueryData): Promise<T | ErrorResponse> {
    return this.post<T>(`query/${entity}`, {
      ...query,
    });
  }

  public meta(entity: string, fields: string, full: boolean = false): Promise<MetaResponse | ErrorResponse> {
    return this.fetch<MetaResponse>(`meta/${entity}?fields=${fields}${full ? '&meta=full' : ''}`);
  }

  public update<T>(entity: string, id: number, data: T): Promise<EntityChangeResponse | ErrorResponse> {
    return this.post<EntityChangeResponse>(`entity/${entity}/${id}`, data);
  }

  public post<T>(url: string, postData: any): Promise<T | ErrorResponse> {
    return this.appBridgeService.promise().then((appBridge) => {
      return appBridge.httpPOST(url, postData).then((response) => {
        if (response.error) {
          console.warn(response.error);
          return response as ErrorResponse;
        }
        return response as T;
      });
    });
  }

  public create<T>(entity: string, putData: T): Promise<EntityChangeResponse | ErrorResponse> {
    return this.appBridgeService.promise().then((appBridge) => {
      return appBridge.httpPUT(`entity/${entity}`, putData).then((response) => {
        if (response.error) {
          console.warn(response.error);
          return response as ErrorResponse;
        }
        return response as EntityChangeResponse;
      });
    });
  }

  public fetch<T>(url: string): Promise<T | ErrorResponse> {
    return this.appBridgeService.promise().then((appBridge) => {
      return appBridge.httpGET(url).then((response) => {
        if (response.error) {
          console.warn(response.error);
          return response as ErrorResponse;
        }
        return response as T;
      });
    });
  }

  public fetchEntity<T>(entity: string, id: number, fields: string): Promise<EntityResponse<T> | ErrorResponse> {
    return this.fetch<EntityResponse<T>>(`entity/${entity}/${id}?fields=${fields}`);
  }
}
