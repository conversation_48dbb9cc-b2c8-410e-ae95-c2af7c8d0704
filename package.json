{"name": "motion-extensions", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "start:local": "ng serve --configuration=staging --ssl", "build": "ng build --configuration=prod", "build:bhdev": "ng build --configuration=bhdev", "build:cchdev": "ng build --configuration=cchdev", "build:train": "ng build --configuration=train", "build:staging": "ng build --configuration=staging", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "precommit": "lint-staged"}, "private": true, "lint-staged": {"*.ts": ["tslint"]}, "dependencies": {"@angular/animations": "13.3.11", "@angular/cdk": "13.3.9", "@angular/common": "13.3.11", "@angular/compiler": "13.3.11", "@angular/core": "13.3.11", "@angular/forms": "13.3.11", "@angular/platform-browser": "13.3.11", "@angular/platform-browser-dynamic": "13.3.11", "@angular/router": "13.3.11", "@bullhorn/dragula": "^1.0.0", "angular-imask": "6.4.2", "angular2-text-mask": "^9.0.0", "brace": "^0.11.0", "clean": "^4.0.2", "core-js": "^2.6.0", "date-fns": "^1.29.0", "geolib": "^3.3.3", "hint.css": "^2.7.0", "imask": "^6.4.2", "moment": "^2.24.0", "moment-timezone": "^0.5.45", "ngx-custom-validators": "^9.1.0", "novo-design-tokens": "0.0.9", "novo-elements": "7.6.0", "post-robot": "^8.0.0", "python": "0.0.4", "rxjs": "6.6.7", "sha.js": "^2.4.11", "text-mask-addons": "^3.7.0", "timezone-support": "^2.0.2", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.3.9", "@angular/cli": "^13.3.9", "@angular/compiler-cli": "13.3.11", "@angular/language-service": "13.3.11", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.2", "@types/node": "^10.12.12", "codelyzer": "^6.0.0", "husky": "^0.14.3", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "1.5.0", "lint-staged": "^7.2.2", "protractor": "~7.0.0", "sass": "^1.60.0", "ts-node": "~5.0.1", "tslint": "~6.1.0", "typescript": "~4.6.4"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}