# Extension Tab for Company Record

## Issues Identified with the Current Custom Tab

**Problem Statement:**
The Agreement tab was created using a delivered custom tab from Bullhorn. The custom tab comes with a select number of field and field types, with limitations of what and how information can display.

**Proposed Solution:**
Create an “extension” tab that will allow for a variety of field and field types allowing more information to display in the format required.

<!-- prettier-ignore -->
| # | Problem Area | Business Impact | Workaround:| Reference |
| - | - | - | - | - |
| 1 | Conversion Schedule | Conversion Tables can not display on the Overview of the Agreement tab, but can on the Edit tab. | User clicks on Edit tab to see conversion schedule in table format. | Appendix A & Appendix A2 |
| 2 | General Comments | New General Comments entered or comments that are converted; will not display as formatted on the Overview tab. You must go into the Edit tab to review the formatted information. | User clicks on Edit tab to see the formatted general comments. | Appendix A & Appendix A2 |
| 3 | Contact/User Names | User/Contact Ids are not dynamic; can not be clicked on/hovered over to see relevant data regarding the contact selected. As delivered, it is only a text name. | User searches for the contact in the contacts tab of the company to see  | Appendix B |
| 4 | Field Types | "There have been several requests out of UAT for additional fields on the Agreement tab. These can not be configured within the current custom tab. The maxed field/field types have been reached.  Examples: Rebate Type, Agreement Status, Discount Type, "| These would need to be added to the Company record (if fields exist or another record type to house this information if required)| |
| 5 | Solutions vs. Staffing Agreement Types | Agreement Types are configured, but all fields display, regardless of Agreement type. Using an Extension gives us flexibility to customize fields to be required and/or display depending on the Agreement Type selected. | User would need to know what fields are required by Agreement Type they are entering. | |
| 6 | Conversion Templates | Each time a conversion table is entered, a table must be added and built out to display the conversion schedule information to display. An extension would allow for conversion schedule templates to be selected from, eliminating the need to re-create each time.| User would need to create the conversion schedule each time a conversion schedule is required to document in the Agreement.| |
| 7 | Effective Dated Agreements | The current Agreement tab, does not allow for true effective dating Agreements for like Agreement Types, in a comprehensive view; displaying only the active version (current effective date).When adding effective dates in the custom tab today, it creates a list of all agreements; and displays all active and inactive. With the effective dated agreements when a new version is created, the old version data would be copied and the user can modify the data, and the newest version would display.| User would create a new Agreement and they would all display in the list, regardless of Agreement Type and if inactive.  | Appendix C |

<!-- prettier-ignore-end -->

## Solution Ideation for #1-6

Using this custom extension, add a new route to display as a custom tab on the Company record.

**Rough Notes:**

- Create a new custom tab called “Agreements”
- Current fields that are currently used:
  - Title (string)
  - Agreement Type (multi-select)
  - Client Logo and Name Approval (boolean)
  - Agreement owner (user)
  - Effective Date
  - Expiration Date
  - Flat Fee
  - Conversion Schedule (table)
  - General Comments (formatted)
- New fields to be added:
  - Rebate Type
  - Agreement Status
  - Discount Type

### Implementation ideas

General notes:

- add a new route/path to `src/app/app.module.ts` with path `agreements`
- create a new module in `src/app/agreements` called `agreements.module.ts` with a class called `AgreementsModule`
- view will have a primary component called `AgreementsComponent`
  - component will have two modes:
    - overview
    - edit
  - overview will display the data in a read-only format
  - edit will allow the user to edit the data

Issue #1: Conversion Schedule
